# StokeFlow Environment Variables
# Copy this file to .env.local for local development
# Set these in Netlify environment variables for production

# Application Environment
VITE_APP_ENV=development

# HighLevel CRM Integration (Optional)
# Get these from your HighLevel account settings
VITE_HIGHLEVEL_PRIVATE_TOKEN=your_private_integration_token_here
VITE_HIGHLEVEL_LOCATION_ID=your_location_id_here
VITE_HIGHLEVEL_DEFAULT_WORKFLOW_ID=your_workflow_id_here

# Analytics Configuration
VITE_ANALYTICS_ENABLED=true

# Form Configuration
VITE_DEFAULT_FORM_COLOR=#3B82F6
VITE_MAX_FORMS_PER_USER=50

# Custom Domain (Production)
VITE_APP_DOMAIN=https://your-domain.netlify.app

# Feature Flags
VITE_ENABLE_TEMPLATES=true
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_INTEGRATIONS=true
