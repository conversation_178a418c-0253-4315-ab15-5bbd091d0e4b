[build]
  publish = "dist"
  command = "npm run build"

[build.environment]
  NODE_VERSION = "18"

# Redirect all routes to index.html for SPA routing
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# Security headers
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "SAMEORIGIN"
    X-Content-Type-Options = "nosniff"
    X-XSS-Protection = "1; mode=block"
    Referrer-Policy = "strict-origin-when-cross-origin"

# Cache static assets
[[headers]]
  for = "/assets/*"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"

# Form submission handling (for contact forms)
[[forms]]
  name = "contact"
  action = "/thank-you"
  
# Environment variables for production
[context.production.environment]
  VITE_APP_ENV = "production"
  VITE_ANALYTICS_ENABLED = "true"

[context.deploy-preview.environment]
  VITE_APP_ENV = "preview"

[context.branch-deploy.environment]
  VITE_APP_ENV = "development"
