@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: Inter, system-ui, sans-serif;
  }
  
  body {
    @apply text-slate-800 bg-slate-50;
  }
  
  h1, h2, h3, h4, h5, h6 {
    @apply font-semibold;
  }
  
  h1 {
    @apply text-3xl md:text-4xl;
  }
  
  h2 {
    @apply text-2xl md:text-3xl;
  }
  
  h3 {
    @apply text-xl md:text-2xl;
  }
  
  h4 {
    @apply text-lg md:text-xl;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 rounded-lg font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2;
  }
  
  .btn-primary {
    @apply btn bg-primary-600 hover:bg-primary-700 text-white focus:ring-primary-500;
  }
  
  .btn-secondary {
    @apply btn bg-white hover:bg-slate-50 text-slate-800 border border-slate-200 focus:ring-primary-500;
  }
  
  .btn-accent {
    @apply btn bg-accent-500 hover:bg-accent-600 text-white focus:ring-accent-500;
  }
  
  .input-field {
    @apply w-full px-4 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent;
  }
  
  .card {
    @apply bg-white rounded-xl shadow-sm border border-slate-200 overflow-hidden;
  }
  
  .form-control {
    @apply mb-4;
  }
  
  .form-label {
    @apply block text-sm font-medium text-slate-700 mb-1;
  }
}