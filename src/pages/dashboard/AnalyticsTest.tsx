import { useAnalyticsStore } from '../../stores/analyticsStore';
import { useFormStore } from '../../stores/formStore';

const AnalyticsTest = () => {
  const { events, formAnalytics, refreshAllAnalytics } = useAnalyticsStore();
  const { forms } = useFormStore();

  const handleRefresh = () => {
    refreshAllAnalytics();
  };

  const handleClearData = () => {
    localStorage.removeItem('analytics-storage');
    window.location.reload();
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-slate-900">Analytics Test Dashboard</h1>
        <div className="space-x-2">
          <button onClick={handleRefresh} className="btn-secondary">
            Refresh Analytics
          </button>
          <button onClick={handleClearData} className="btn-secondary text-red-600">
            Clear All Data
          </button>
        </div>
      </div>

      {/* Raw Events */}
      <div className="card">
        <div className="p-4 border-b">
          <h2 className="text-lg font-semibold">Raw Analytics Events ({events.length})</h2>
        </div>
        <div className="p-4">
          {events.length === 0 ? (
            <p className="text-slate-500">No events tracked yet. Visit a form to start tracking!</p>
          ) : (
            <div className="space-y-2 max-h-64 overflow-y-auto">
              {events.slice(-10).reverse().map(event => (
                <div key={event.id} className="text-xs bg-slate-50 p-2 rounded">
                  <div className="font-mono">
                    <span className="font-semibold">{event.eventType}</span> | 
                    Form: {event.formId} | 
                    {event.stepId && `Step: ${event.stepId} | `}
                    Session: {event.sessionId.slice(-8)} | 
                    {new Date(event.timestamp).toLocaleTimeString()}
                  </div>
                </div>
              ))}
              {events.length > 10 && (
                <p className="text-xs text-slate-400">Showing last 10 events of {events.length} total</p>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Calculated Analytics */}
      <div className="card">
        <div className="p-4 border-b">
          <h2 className="text-lg font-semibold">Calculated Analytics</h2>
        </div>
        <div className="p-4">
          {Object.keys(formAnalytics).length === 0 ? (
            <p className="text-slate-500">No analytics calculated yet.</p>
          ) : (
            <div className="space-y-4">
              {Object.values(formAnalytics).map(analytics => {
                const form = forms.find(f => f.id === analytics.formId);
                return (
                  <div key={analytics.formId} className="border rounded p-4">
                    <h3 className="font-semibold mb-2">
                      {form?.name || analytics.formId}
                    </h3>
                    <div className="grid grid-cols-3 gap-4 mb-4">
                      <div>
                        <div className="text-sm text-slate-500">Views</div>
                        <div className="text-xl font-semibold">{analytics.totalViews}</div>
                      </div>
                      <div>
                        <div className="text-sm text-slate-500">Submissions</div>
                        <div className="text-xl font-semibold">{analytics.totalSubmissions}</div>
                      </div>
                      <div>
                        <div className="text-sm text-slate-500">Conversion Rate</div>
                        <div className="text-xl font-semibold">{analytics.conversionRate}%</div>
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <h4 className="font-medium">Step Analytics:</h4>
                      {analytics.stepAnalytics.map(step => (
                        <div key={step.stepId} className="text-sm bg-slate-50 p-2 rounded">
                          <div className="font-medium">{step.stepName}</div>
                          <div className="text-slate-600">
                            Views: {step.views} | 
                            Completions: {step.completions} | 
                            Exits: {step.exits} | 
                            Drop-off: {step.dropOffRate}%
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </div>

      {/* Instructions */}
      <div className="card bg-blue-50">
        <div className="p-4">
          <h2 className="text-lg font-semibold mb-2">How to Test Real Analytics</h2>
          <ol className="list-decimal list-inside space-y-1 text-sm">
            <li>Open the form preview: <a href="/form/test-highlevel" className="text-blue-600 underline" target="_blank">/form/test-highlevel</a></li>
            <li>Navigate through the form steps (this tracks step views and completions)</li>
            <li>Submit the form (this tracks form submission)</li>
            <li>Come back to this page and click "Refresh Analytics" to see the data</li>
            <li>Check the main Analytics page to see the real data in the dashboard</li>
          </ol>
        </div>
      </div>
    </div>
  );
};

export default AnalyticsTest;
