<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>StokeFlow Form Embedding Test</title>
    <style>
      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          sans-serif;
        line-height: 1.6;
        margin: 0;
        padding: 20px;
        background-color: #f8fafc;
      }
      .container {
        max-width: 1200px;
        margin: 0 auto;
      }
      .header {
        text-align: center;
        margin-bottom: 40px;
        padding: 40px 20px;
        background: white;
        border-radius: 12px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      }
      .embed-section {
        margin-bottom: 40px;
        padding: 30px;
        background: white;
        border-radius: 12px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      }
      .embed-section h2 {
        color: #1e293b;
        margin-bottom: 10px;
      }
      .embed-section p {
        color: #64748b;
        margin-bottom: 20px;
      }
      .form-container {
        border: 2px dashed #e2e8f0;
        border-radius: 8px;
        padding: 20px;
        background: #f8fafc;
      }
      iframe {
        border: none;
        border-radius: 8px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      }
      .code-block {
        background: #1e293b;
        color: #e2e8f0;
        padding: 15px;
        border-radius: 8px;
        font-family: "Monaco", "Menlo", monospace;
        font-size: 14px;
        overflow-x: auto;
        margin: 15px 0;
      }
      .button {
        background: #3b82f6;
        color: white;
        border: none;
        padding: 12px 24px;
        border-radius: 8px;
        cursor: pointer;
        font-size: 16px;
        margin: 10px 5px;
      }
      .button:hover {
        background: #2563eb;
      }
      .button.secondary {
        background: #64748b;
      }
      .button.secondary:hover {
        background: #475569;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <h1>🚀 StokeFlow Form Embedding Test</h1>
        <p>Test different ways to embed StokeFlow forms on your website</p>
      </div>

      <!-- Simple iframe Embed -->
      <div class="embed-section">
        <h2>1. Simple iframe Embed</h2>
        <p>Basic iframe embedding - works on any website</p>

        <div class="code-block">
          &lt;iframe src="https://stokeflow.netlify.app/form/test-highlevel"
          width="100%" height="600" frameborder="0"&gt; &lt;/iframe&gt;
        </div>

        <div class="form-container">
          <iframe
            src="https://stokeflow.netlify.app/form/test-highlevel"
            width="100%"
            height="600"
            frameborder="0"
          >
          </iframe>
        </div>
      </div>

      <!-- Responsive iframe -->
      <div class="embed-section">
        <h2>2. Responsive iframe (Auto-resize)</h2>
        <p>Automatically adjusts height based on form content</p>

        <div class="code-block">
          &lt;iframe id="stokeflow-form-responsive"
          src="https://stokeflow.netlify.app/form/modern-lead-template"
          width="100%" height="600" frameborder="0"&gt; &lt;/iframe&gt;
          &lt;script&gt; window.addEventListener('message', function(event) { if
          (event.data.type === 'resize') { const iframe =
          document.getElementById('stokeflow-form-responsive'); if (iframe) {
          iframe.style.height = event.data.height + 'px'; } } });
          &lt;/script&gt;
        </div>

        <div class="form-container">
          <iframe
            id="stokeflow-form-responsive"
            src="https://stokeflow.netlify.app/form/modern-lead-template"
            width="100%"
            height="600"
            frameborder="0"
          >
          </iframe>
        </div>
      </div>

      <!-- Modal/Popup Embed -->
      <div class="embed-section">
        <h2>3. Modal/Popup Embed</h2>
        <p>Open forms in a modal overlay - great for CTAs</p>

        <button class="button" onclick="openFormModal()">
          📝 Open Form in Modal
        </button>

        <div class="code-block">
          &lt;button onclick="openStokeFlowForm()"&gt;Get Quote&lt;/button&gt;
          &lt;script&gt; function openStokeFlowForm() { var modal =
          document.createElement('div'); modal.style.cssText = ` position:
          fixed; top: 0; left: 0; width: 100%; height: 100%; background:
          rgba(0,0,0,0.8); z-index: 10000; display: flex; align-items: center;
          justify-content: center; `; var iframe =
          document.createElement('iframe'); iframe.src =
          'https://stokeflow.netlify.app/form/test-highlevel';
          iframe.style.cssText = ` width: 90%; max-width: 800px; height: 90%;
          max-height: 600px; border: none; border-radius: 12px; background:
          white; `; modal.appendChild(iframe); modal.onclick = function(e) { if
          (e.target === modal) document.body.removeChild(modal); };
          document.body.appendChild(modal); } &lt;/script&gt;
        </div>
      </div>

      <!-- Native JavaScript Embed (No iframe) -->
      <div class="embed-section">
        <h2>4. Native JavaScript Embed (Recommended)</h2>
        <p>
          Embeds the form directly into your page DOM - fully responsive and
          seamless
        </p>

        <div class="code-block">
          &lt;!-- Method 1: Auto-initialize with data attribute --&gt; &lt;div
          data-stokeflow-form="test-highlevel"&gt;&lt;/div&gt; &lt;script
          src="https://stokeflow.netlify.app/stokeflow-widget.js"&gt;&lt;/script&gt;
          &lt;!-- Method 2: Manual initialization --&gt; &lt;div
          id="my-form"&gt;&lt;/div&gt; &lt;script
          src="https://stokeflow.netlify.app/stokeflow-widget.js"&gt;&lt;/script&gt;
          &lt;script&gt; const widget = StokeFlow.create({ formId:
          'modern-lead-template', containerId: 'my-form', onSubmit:
          function(data) { console.log('Form submitted:', data); // Handle form
          submission }, theme: { primaryColor: '#10B981' } }); &lt;/script&gt;
        </div>

        <div class="form-container">
          <h4>Auto-initialized Form:</h4>
          <div data-stokeflow-form="test-highlevel"></div>

          <h4 style="margin-top: 40px">Manual Form:</h4>
          <div id="manual-form"></div>
        </div>
      </div>

      <!-- Direct Link -->
      <div class="embed-section">
        <h2>5. Direct Link</h2>
        <p>Link directly to the form page</p>

        <a
          href="https://stokeflow.netlify.app/form/test-highlevel"
          target="_blank"
          class="button"
        >
          🔗 Open Form in New Tab
        </a>

        <a
          href="https://stokeflow.netlify.app/form/modern-lead-template"
          target="_blank"
          class="button secondary"
        >
          🔗 Open Multi-Step Form
        </a>
      </div>

      <!-- Available Forms -->
      <div class="embed-section">
        <h2>📋 Available Test Forms</h2>
        <p>These forms are available for testing:</p>

        <ul style="color: #64748b">
          <li>
            <strong>test-highlevel</strong> - Simple contact form with HighLevel
            integration
          </li>
          <li>
            <strong>modern-lead-template</strong> - Multi-step lead generation
            form with 8 steps
          </li>
        </ul>

        <p style="font-size: 14px; color: #64748b">
          Replace the form ID in the URLs above with any form ID from your
          StokeFlow dashboard.
        </p>
      </div>
    </div>

    <!-- Load StokeFlow Widget -->
    <script src="https://stokeflow.netlify.app/stokeflow-widget.js"></script>

    <script>
      // Auto-resize functionality for responsive iframe
      window.addEventListener("message", function (event) {
        if (event.data.type === "resize") {
          const iframe = document.getElementById("stokeflow-form-responsive");
          if (iframe) {
            iframe.style.height = event.data.height + "px";
          }
        }
      });

      // Modal functionality
      function openFormModal() {
        var modal = document.createElement("div");
        modal.style.cssText = `
                position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                background: rgba(0,0,0,0.8); z-index: 10000; display: flex;
                align-items: center; justify-content: center; padding: 20px;
            `;

        var iframe = document.createElement("iframe");
        iframe.src = "https://stokeflow.netlify.app/form/test-highlevel";
        iframe.style.cssText = `
                width: 100%; max-width: 800px; height: 90vh; max-height: 600px;
                border: none; border-radius: 12px; background: white;
            `;

        var closeButton = document.createElement("button");
        closeButton.innerHTML = "✕";
        closeButton.style.cssText = `
                position: absolute; top: 10px; right: 10px; background: white;
                border: none; width: 40px; height: 40px; border-radius: 50%;
                cursor: pointer; font-size: 18px; z-index: 10001;
            `;
        closeButton.onclick = function () {
          document.body.removeChild(modal);
        };

        modal.appendChild(iframe);
        modal.appendChild(closeButton);
        modal.onclick = function (e) {
          if (e.target === modal) document.body.removeChild(modal);
        };

        document.body.appendChild(modal);
      }

      // Initialize manual JavaScript form
      document.addEventListener("DOMContentLoaded", function () {
        // Create manual form with custom options
        const manualWidget = StokeFlow.create({
          formId: "modern-lead-template",
          containerId: "manual-form",
          onSubmit: function (data) {
            console.log("Manual form submitted:", data);
            alert("Form submitted! Check console for data.");
          },
          onLoad: function (form) {
            console.log("Manual form loaded:", form);
          },
          theme: {
            primaryColor: "#10B981",
          },
        });

        // Store reference for debugging
        window.manualWidget = manualWidget;
      });
    </script>
  </body>
</html>
